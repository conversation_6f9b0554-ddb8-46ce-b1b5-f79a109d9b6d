#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回收站修复工具
用于诊断和修复回收站中无法还原的文件问题
"""

import os
import sys
import json
from pathlib import Path
import shutil
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_manager import ConfigManager
from models.filesystem_manager import FileSystemManager
from utils.file_operations import FileOperations


class RecycleBinRepair:
    def __init__(self):
        """初始化修复工具"""
        self.config_manager = ConfigManager()
        self.storage_path = Path(self.config_manager.get_storage_path())
        self.fs_manager = FileSystemManager(str(self.storage_path))
        self.file_operations = FileOperations(self.config_manager, self.fs_manager)
        self.recycle_path = self.storage_path / "回收站"
        
    def diagnose_recycle_bin(self):
        """诊断回收站状态"""
        print("=" * 50)
        print("回收站诊断报告")
        print("=" * 50)
        
        # 检查回收站目录是否存在
        print(f"回收站路径: {self.recycle_path}")
        print(f"回收站目录存在: {self.recycle_path.exists()}")
        
        if not self.recycle_path.exists():
            print("创建回收站目录...")
            self.recycle_path.mkdir(exist_ok=True)
        
        # 获取回收站中的实际文件
        actual_files = []
        if self.recycle_path.exists():
            for file_path in self.recycle_path.iterdir():
                if file_path.is_file() and not file_path.name.startswith('.'):
                    actual_files.append(file_path)
        
        print(f"回收站实际文件数量: {len(actual_files)}")
        for file_path in actual_files:
            print(f"  - {file_path.name}")
        
        # 获取元数据中的回收站文件
        metadata_files = self.fs_manager.get_files_by_category("回收站")
        print(f"元数据中的回收站文件数量: {len(metadata_files)}")
        
        # 检查特定的问题文件
        problem_files = ["20241202101441451.psd", "海岛.psd"]
        print(f"\n检查问题文件:")
        
        for filename in problem_files:
            print(f"\n文件: {filename}")
            
            # 检查文件是否在回收站目录中
            file_path = self.recycle_path / filename
            print(f"  在回收站目录中: {file_path.exists()}")
            
            # 检查是否在元数据中
            found_in_metadata = False
            file_info = None
            for info in metadata_files:
                if info['name'] == filename:
                    found_in_metadata = True
                    file_info = info
                    break
            
            print(f"  在元数据中: {found_in_metadata}")
            if file_info:
                print(f"  文件ID: {file_info['id']}")
                print(f"  原始路径: {file_info.get('original_path', '未知')}")
                print(f"  删除时间: {file_info.get('delete_time', '未知')}")
        
        return actual_files, metadata_files, problem_files
    
    def repair_recycle_bin(self):
        """修复回收站"""
        print("\n" + "=" * 50)
        print("开始修复回收站")
        print("=" * 50)
        
        # 先进行诊断
        actual_files, metadata_files, problem_files = self.diagnose_recycle_bin()
        
        # 清理不存在的文件记录
        print("\n1. 清理无效的元数据记录...")
        cleaned_count = self.fs_manager.cleanup_non_existent_files()
        print(f"清理了 {cleaned_count} 个无效记录")
        
        # 为存在但没有元数据的文件创建元数据
        print("\n2. 为缺失元数据的文件创建记录...")
        metadata_file_names = {info['name'] for info in metadata_files}
        
        for file_path in actual_files:
            if file_path.name not in metadata_file_names:
                print(f"为文件创建元数据: {file_path.name}")
                self._create_metadata_for_file(file_path)
        
        # 尝试修复特定的问题文件
        print("\n3. 修复特定问题文件...")
        for filename in problem_files:
            self._repair_specific_file(filename)
        
        print("\n修复完成!")
    
    def _create_metadata_for_file(self, file_path: Path):
        """为文件创建元数据"""
        try:
            # 生成文件ID
            file_id = self.fs_manager._get_file_id(str(file_path))
            
            # 推断原始路径
            original_path = self._guess_original_path(file_path.name)
            
            # 获取文件统计信息
            stat = file_path.stat()
            
            # 创建元数据
            recycle_metadata = self.fs_manager._load_metadata("回收站")
            recycle_metadata[file_id] = {
                'original_path': original_path,
                'delete_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'custom_color': None,
                'tags': []
            }
            
            # 保存元数据
            self.fs_manager._save_metadata("回收站", recycle_metadata)
            print(f"成功为 {file_path.name} 创建元数据")
            
        except Exception as e:
            print(f"为 {file_path.name} 创建元数据失败: {e}")
    
    def _guess_original_path(self, filename: str) -> str:
        """推断文件的原始路径"""
        file_ext = Path(filename).suffix.lower()
        
        # PSD文件通常是图片类型
        if file_ext in ['.psd', '.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            # 检查各个图片分类目录是否存在
            for category in ['人物', '场景', '道具']:
                category_path = self.storage_path / category
                if category_path.exists():
                    return str(category_path / filename)
            # 默认返回人物分类
            return str(self.storage_path / '人物' / filename)
        else:
            # 其他文件类型
            return str(self.storage_path / '其他' / filename)
    
    def _repair_specific_file(self, filename: str):
        """修复特定文件"""
        print(f"\n修复文件: {filename}")
        
        file_path = self.recycle_path / filename
        
        if not file_path.exists():
            print(f"  文件不存在于回收站: {file_path}")
            return
        
        # 检查是否有元数据
        metadata_files = self.fs_manager.get_files_by_category("回收站")
        file_info = None
        
        for info in metadata_files:
            if info['name'] == filename:
                file_info = info
                break
        
        if not file_info:
            print(f"  没有找到元数据，创建新的元数据...")
            self._create_metadata_for_file(file_path)
            
            # 重新获取文件信息
            metadata_files = self.fs_manager.get_files_by_category("回收站")
            for info in metadata_files:
                if info['name'] == filename:
                    file_info = info
                    break
        
        if file_info:
            print(f"  找到文件信息: {file_info['id']}")
            print(f"  尝试还原文件...")
            
            # 尝试还原
            success = self.file_operations.restore_file(file_info['id'])
            if success:
                print(f"  ✓ 文件 {filename} 还原成功!")
            else:
                print(f"  ✗ 文件 {filename} 还原失败")
        else:
            print(f"  无法创建或找到文件元数据")
    
    def list_recycle_bin_files(self):
        """列出回收站中的所有文件"""
        print("=" * 50)
        print("回收站文件列表")
        print("=" * 50)
        
        files = self.fs_manager.get_files_by_category("回收站")
        
        if not files:
            print("回收站为空")
            return
        
        for i, file_info in enumerate(files, 1):
            print(f"{i}. {file_info['name']}")
            print(f"   ID: {file_info['id']}")
            print(f"   路径: {file_info['path']}")
            print(f"   原始路径: {file_info.get('original_path', '未知')}")
            print(f"   删除时间: {file_info.get('delete_time', '未知')}")
            print(f"   文件存在: {Path(file_info['path']).exists()}")
            print()


def main():
    """主函数"""
    repair_tool = RecycleBinRepair()
    
    print("简笔画素材管理 - 回收站修复工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 诊断回收站")
        print("2. 修复回收站")
        print("3. 列出回收站文件")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            repair_tool.diagnose_recycle_bin()
        elif choice == '2':
            repair_tool.repair_recycle_bin()
        elif choice == '3':
            repair_tool.list_recycle_bin_files()
        elif choice == '4':
            print("退出修复工具")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
