#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理回收站元数据工具
用于清理已经还原但元数据未正确更新的文件记录
"""

import os
import sys
import json
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_manager import ConfigManager
from models.filesystem_manager import FileSystemManager


def cleanup_recycle_metadata():
    """清理回收站元数据"""
    print("开始清理回收站元数据...")
    
    # 初始化管理器
    config_manager = ConfigManager()
    storage_path = Path(config_manager.get_storage_path())
    fs_manager = FileSystemManager(str(storage_path))
    
    # 清理不存在的文件记录
    cleaned_count = fs_manager.cleanup_non_existent_files()
    print(f"清理了 {cleaned_count} 个无效的文件记录")
    
    # 检查回收站状态
    recycle_files = fs_manager.get_files_by_category("回收站")
    print(f"回收站中剩余文件数量: {len(recycle_files)}")
    
    for file_info in recycle_files:
        print(f"  - {file_info['name']} (存在: {Path(file_info['path']).exists()})")
    
    print("元数据清理完成!")


if __name__ == "__main__":
    cleanup_recycle_metadata()
